const sequelize = require('../src/config/database');
const QueryPerformanceMonitor = require('../src/utils/queryPerformanceMonitor');
const { UserProfile, School } = require('../src/models');

/**
 * Test script untuk mengukur performa index yang baru
 */

const monitor = new QueryPerformanceMonitor(sequelize);

async function testIndexPerformance() {
  try {
    console.log('🚀 Starting index performance tests...\n');

    // Test 1: Gender filter performance
    console.log('📊 Testing gender filter performance...');
    await monitor.monitorQuery('gender_filter_test', async () => {
      return UserProfile.findAll({
        where: { gender: 'male' },
        limit: 100
      });
    });

    // Test 2: Date of birth range performance
    console.log('📊 Testing date of birth range performance...');
    await monitor.monitorQuery('date_range_test', async () => {
      const { Op } = require('sequelize');
      return UserProfile.findAll({
        where: {
          date_of_birth: {
            [Op.between]: ['1990-01-01', '2000-12-31']
          }
        },
        limit: 100
      });
    });

    // Test 3: School origin search performance
    console.log('📊 Testing school origin search performance...');
    await monitor.monitorQuery('school_search_test', async () => {
      const { Op } = require('sequelize');
      return UserProfile.findAll({
        where: {
          school_origin: {
            [Op.iLike]: '%University%'
          }
        },
        limit: 100
      });
    });

    // Test 4: Composite demographic query
    console.log('📊 Testing composite demographic query...');
    await monitor.monitorQuery('composite_demographic_test', async () => {
      const { Op } = require('sequelize');
      return UserProfile.findAll({
        where: {
          gender: 'female',
          date_of_birth: {
            [Op.between]: ['1995-01-01', '2005-12-31']
          },
          school_origin: {
            [Op.ne]: null
          }
        },
        limit: 100
      });
    });

    // Test 5: School location search
    console.log('📊 Testing school location search...');
    await monitor.monitorQuery('school_location_test', async () => {
      return School.searchByLocation('DKI Jakarta', 'Jakarta Selatan', 50);
    });

    // Test 6: School case-insensitive search
    console.log('📊 Testing school case-insensitive search...');
    await monitor.monitorQuery('school_case_insensitive_test', async () => {
      return School.searchOptimized({
        search: 'universitas',
        limit: 50
      });
    });

    // Test 7: UserProfile demographic methods
    console.log('📊 Testing UserProfile demographic methods...');
    await monitor.monitorQuery('gender_distribution_test', async () => {
      return UserProfile.getGenderDistribution();
    });

    await monitor.monitorQuery('age_distribution_test', async () => {
      return UserProfile.getAgeDistribution();
    });

    await monitor.monitorQuery('top_schools_test', async () => {
      return UserProfile.getTopSchools(10);
    });

    // Generate performance report
    console.log('\n📈 Generating performance report...');
    const report = await monitor.generatePerformanceReport();
    
    console.log('\n=== PERFORMANCE REPORT ===');
    console.log('Timestamp:', report.timestamp);
    
    console.log('\n📊 Query Performance:');
    Object.entries(report.queryPerformance).forEach(([queryName, stats]) => {
      console.log(`  ${queryName}:`);
      console.log(`    Average time: ${stats.avgTime.toFixed(2)}ms`);
      console.log(`    Min time: ${stats.minTime}ms`);
      console.log(`    Max time: ${stats.maxTime}ms`);
      console.log(`    Total executions: ${stats.count}`);
      console.log(`    Slow queries: ${stats.slowQueryPercentage.toFixed(1)}%`);
      console.log('');
    });

    console.log('\n🔍 Index Usage:');
    report.indexUsage.forEach(index => {
      console.log(`  ${index.schemaname}.${index.tablename}.${index.indexname}:`);
      console.log(`    Scans: ${index.scans}`);
      console.log(`    Usage level: ${index.usage_level}`);
      console.log(`    Tuples read: ${index.tuples_read}`);
      console.log('');
    });

    console.log('\n📋 Table Scan Patterns:');
    report.tableScanPatterns.forEach(table => {
      console.log(`  ${table.schemaname}.${table.tablename}:`);
      console.log(`    Sequential scans: ${table.sequential_scans}`);
      console.log(`    Index scans: ${table.index_scans}`);
      console.log(`    Scan pattern: ${table.scan_pattern}`);
      console.log('');
    });

    console.log('\n💡 Recommendations:');
    if (report.recommendations.length === 0) {
      console.log('  ✅ No performance issues detected!');
    } else {
      report.recommendations.forEach(rec => {
        console.log(`  ${rec.severity}: ${rec.message}`);
        console.log(`    Suggestion: ${rec.suggestion}`);
        console.log('');
      });
    }

    console.log('\n✅ Index performance test completed successfully!');

  } catch (error) {
    console.error('❌ Error during index performance test:', error.message);
    console.error(error.stack);
  } finally {
    await sequelize.close();
  }
}

// Test specific index effectiveness
async function testSpecificIndexes() {
  try {
    console.log('\n🔍 Testing specific index effectiveness...\n');

    // Test index usage for specific queries
    const indexTests = [
      {
        name: 'Gender Index Test',
        query: `
          EXPLAIN (ANALYZE, BUFFERS) 
          SELECT * FROM auth.user_profiles 
          WHERE gender = 'male' 
          LIMIT 100
        `
      },
      {
        name: 'Date of Birth Index Test',
        query: `
          EXPLAIN (ANALYZE, BUFFERS) 
          SELECT * FROM auth.user_profiles 
          WHERE date_of_birth BETWEEN '1990-01-01' AND '2000-12-31' 
          LIMIT 100
        `
      },
      {
        name: 'Composite Demographics Index Test',
        query: `
          EXPLAIN (ANALYZE, BUFFERS) 
          SELECT * FROM auth.user_profiles 
          WHERE gender = 'female' 
            AND date_of_birth BETWEEN '1995-01-01' AND '2005-12-31'
            AND school_origin IS NOT NULL
          LIMIT 100
        `
      },
      {
        name: 'School Location Index Test',
        query: `
          EXPLAIN (ANALYZE, BUFFERS) 
          SELECT * FROM public.schools 
          WHERE province = 'DKI Jakarta' 
            AND city = 'Jakarta Selatan'
          LIMIT 50
        `
      }
    ];

    for (const test of indexTests) {
      console.log(`📊 ${test.name}:`);
      try {
        const [results] = await sequelize.query(test.query);
        results.forEach(row => {
          console.log(`  ${row['QUERY PLAN']}`);
        });
        console.log('');
      } catch (error) {
        console.log(`  ❌ Error: ${error.message}\n`);
      }
    }

  } catch (error) {
    console.error('❌ Error during specific index test:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🎯 ATMA Backend - Index Performance Testing');
  console.log('==========================================\n');

  await testIndexPerformance();
  await testSpecificIndexes();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testIndexPerformance,
  testSpecificIndexes
};
