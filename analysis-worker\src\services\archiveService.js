/**
 * Archive Service Integration
 */

const axios = require('axios');
const logger = require('../utils/logger');

// Archive service configuration
const config = {
  baseURL: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
  serviceKey: process.env.ARCHIVE_SERVICE_KEY || 'internal_service_secret_key_change_in_production',
  timeout: 30000 // 30 seconds
};

// Create axios instance
const archiveClient = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json',
    'X-Internal-Service': 'true',
    'X-Service-Key': config.serviceKey
  }
});

// Add request interceptor for logging
archiveClient.interceptors.request.use(
  (config) => {
    logger.debug('Archive service request', {
      method: config.method,
      url: config.url,
      headers: config.headers
    });
    return config;
  },
  (error) => {
    logger.error('Archive service request error', { error: error.message });
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
archiveClient.interceptors.response.use(
  (response) => {
    logger.debug('Archive service response', {
      status: response.status,
      statusText: response.statusText,
      url: response.config.url
    });
    return response;
  },
  (error) => {
    logger.error('Archive service response error', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      error: error.message
    });
    return Promise.reject(error);
  }
);

/**
 * Save analysis result to Archive Service
 * @param {String} userId - User ID
 * @param {Object} assessmentData - Assessment data
 * @param {Object} personaProfile - Persona profile
 * @param {String} jobId - Job ID for logging
 * @param {Boolean} useBatch - Whether to use batch processing (default: true)
 * @returns {Promise<Object>} - Save result
 */
const saveAnalysisResult = async (userId, assessmentData, personaProfile, jobId, useBatch = true) => {
  try {
    logger.info('Saving analysis result to Archive Service', {
      jobId,
      userId,
      profileArchetype: personaProfile.archetype,
      useBatch
    });

    // Prepare request body
    const requestBody = {
      user_id: userId,
      assessment_data: assessmentData,
      persona_profile: personaProfile,
      status: 'completed'
    };

    // Prepare query parameters for batch processing
    const queryParams = useBatch ? '?batch=true' : '';

    // Send request to Archive Service
    const response = await archiveClient.post(`/archive/results${queryParams}`, requestBody);

    logger.info('Analysis result saved successfully', {
      jobId,
      userId,
      resultId: response.data.data.id,
      status: response.status,
      batched: response.data.data.batched || false
    });

    return {
      success: true,
      id: response.data.data.id,
      status: response.data.data.status,
      created_at: response.data.data.created_at,
      batched: response.data.data.batched || false
    };

  } catch (error) {
    logger.error('Failed to save analysis result', {
      jobId,
      userId,
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    // Check if it's a retryable error
    const isRetryable = isRetryableError(error);

    throw {
      message: error.message,
      isRetryable,
      status: error.response?.status,
      originalError: error
    };
  }
};

/**
 * Update analysis result status
 * @param {String} resultId - Result ID
 * @param {String} status - New status
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Object>} - Update result
 */
const updateAnalysisResult = async (resultId, status, jobId) => {
  try {
    logger.info('Updating analysis result status', {
      jobId,
      resultId,
      status
    });

    // Prepare request body
    const requestBody = {
      status
    };

    // Send request to Archive Service
    const response = await archiveClient.put(`/archive/results/${resultId}`, requestBody);

    logger.info('Analysis result status updated successfully', {
      jobId,
      resultId,
      status: response.status
    });

    return {
      success: true,
      id: response.data.data.id,
      updated_at: response.data.data.updated_at
    };

  } catch (error) {
    logger.error('Failed to update analysis result status', {
      jobId,
      resultId,
      status,
      error: error.message,
      responseStatus: error.response?.status
    });

    throw {
      message: error.message,
      isRetryable: isRetryableError(error),
      status: error.response?.status,
      originalError: error
    };
  }
};

/**
 * Check if error is retryable
 * @param {Error} error - Error object
 * @returns {boolean} - Whether error is retryable
 */
const isRetryableError = (error) => {
  // Network errors are retryable
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
    return true;
  }

  // HTTP 5xx errors are retryable
  if (error.response && error.response.status >= 500) {
    return true;
  }

  // HTTP 429 (Too Many Requests) is retryable
  if (error.response && error.response.status === 429) {
    return true;
  }

  // Other errors are not retryable
  return false;
};

/**
 * Save multiple analysis results using batch processing
 * @param {Array} results - Array of result objects
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Object>} - Batch save result
 */
const saveBatchAnalysisResults = async (results, jobId) => {
  try {
    logger.info('Saving batch analysis results to Archive Service', {
      jobId,
      resultCount: results.length
    });

    // Send request to Archive Service batch endpoint
    const response = await archiveClient.post('/archive/results/batch', results);

    logger.info('Batch analysis results saved successfully', {
      jobId,
      total: response.data.data.total,
      successful: response.data.data.successful,
      failed: response.data.data.failed
    });

    return {
      success: true,
      total: response.data.data.total,
      successful: response.data.data.successful,
      failed: response.data.data.failed,
      results: response.data.data.results
    };

  } catch (error) {
    logger.error('Failed to save batch analysis results', {
      jobId,
      resultCount: results.length,
      error: error.message,
      status: error.response?.status
    });

    throw {
      message: error.message,
      isRetryable: isRetryableError(error),
      status: error.response?.status,
      originalError: error
    };
  }
};

/**
 * Force process batch queue in Archive Service
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Object>} - Batch process result
 */
const processBatchQueue = async (jobId) => {
  try {
    logger.info('Forcing batch queue processing in Archive Service', { jobId });

    const response = await archiveClient.post('/archive/batch/process');

    logger.info('Batch queue processed successfully', {
      jobId,
      processedItems: response.data.data.processed_items,
      successful: response.data.data.successful,
      failed: response.data.data.failed
    });

    return {
      success: true,
      processed_items: response.data.data.processed_items,
      successful: response.data.data.successful,
      failed: response.data.data.failed,
      timestamp: response.data.data.timestamp
    };

  } catch (error) {
    logger.error('Failed to process batch queue', {
      jobId,
      error: error.message,
      status: error.response?.status
    });

    throw {
      message: error.message,
      isRetryable: isRetryableError(error),
      status: error.response?.status,
      originalError: error
    };
  }
};

/**
 * Get batch processing statistics
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Object>} - Batch stats
 */
const getBatchStats = async (jobId) => {
  try {
    const response = await archiveClient.get('/archive/batch/stats');

    logger.debug('Retrieved batch processing stats', {
      jobId,
      queueSize: response.data.data.queue_size,
      processing: response.data.data.processing
    });

    return {
      success: true,
      queue_size: response.data.data.queue_size,
      processing: response.data.data.processing,
      last_batch_processed: response.data.data.last_batch_processed,
      total_processed: response.data.data.total_processed
    };

  } catch (error) {
    logger.error('Failed to get batch stats', {
      jobId,
      error: error.message,
      status: error.response?.status
    });

    throw {
      message: error.message,
      isRetryable: isRetryableError(error),
      status: error.response?.status,
      originalError: error
    };
  }
};

/**
 * Check Archive Service health
 * @returns {Promise<boolean>} - Health status
 */
const checkHealth = async () => {
  try {
    const response = await archiveClient.get('/health', {
      timeout: 5000 // 5 seconds for health check
    });

    return response.status === 200;
  } catch (error) {
    logger.error('Archive Service health check failed', { error: error.message });
    return false;
  }
};

module.exports = {
  saveAnalysisResult,
  updateAnalysisResult,
  saveBatchAnalysisResults,
  processBatchQueue,
  getBatchStats,
  checkHealth
};
