# Auth Service API Documentation

## Error Codes
- **400**: Bad Request - Validation error atau parameter tidak valid
- **401**: Unauthorized - Token tidak valid atau tidak ada
- **403**: Forbidden - Tidak memiliki permission untuk akses resource
- **404**: Not Found - Resource tidak ditemukan
- **409**: Conflict - Data sudah ada (misal email sudah terdaftar)
- **500**: Internal Server Error - Error pada server

---

## Public Endpoints (Tidak perlu authentication)

### POST /auth/register
**Deskripsi**: Registrasi user baru
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}
```

### POST /auth/register/batch
**Deskripsi**: Registrasi batch user (untuk testing/seeding)
**Request Body**: Array of user objects
**Response**: Batch processing result

### POST /auth/login
**Deskripsi**: Login user
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}
```

---

## Protected Endpoints (Perlu authentication token)

### GET /auth/profile
**Deskripsi**: Mendapatkan profil user yang sedang login
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "school_origin": "SMA Negeri 1",
      "date_of_birth": "2000-01-01",
      "gender": "male",
      "token_balance": 100
    }
  }
}
```

### PUT /auth/profile
**Deskripsi**: Update profil user
**Request Body**:
```json
{
  "full_name": "John Doe",
  "school_origin": "SMA Negeri 1",
  "date_of_birth": "2000-01-01",
  "gender": "male"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "school_origin": "SMA Negeri 1",
      "date_of_birth": "2000-01-01",
      "gender": "male"
    }
  }
}
```

### POST /auth/change-password
**Deskripsi**: Ganti password user
**Request Body**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

### POST /auth/logout
**Deskripsi**: Logout user (invalidate token di client side)
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### GET /auth/token-balance
**Deskripsi**: Mendapatkan saldo token user
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "token_balance": 100
  }
}
```

---

## Internal Service Endpoints

### POST /auth/verify-token
**Deskripsi**: Verifikasi JWT token (untuk internal service)
**Request Body**:
```json
{
  "token": "jwt_token_here"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    }
  }
}
```

### PUT /auth/token-balance
**Deskripsi**: Update saldo token user (internal service only)
**Request Body**:
```json
{
  "userId": "uuid",
  "amount": 10,
  "operation": "add"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Token balance updated",
  "data": {
    "user_id": "uuid",
    "old_balance": 100,
    "new_balance": 110,
    "amount": 10,
    "operation": "add"
  }
}
```

### GET /auth/batch/registration/stats
**Deskripsi**: Mendapatkan statistik batch registration (internal service only)
**Request Body**: Tidak ada
**Response**: Batch registration statistics

### POST /auth/batch/registration/process
**Deskripsi**: Force process registration batch queue (internal service only)
**Request Body**: Tidak ada
**Response**: Batch processing result

### POST /auth/batch/registration/clear
**Deskripsi**: Clear registration batch queue (internal service only)
**Request Body**: Tidak ada
**Response**: Queue clear result

---

## Admin Auth Endpoints

### Public Admin Endpoints

#### POST /admin/login
**Deskripsi**: Login admin
**Request Body**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "uuid",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "superadmin"
    },
    "token": "jwt_token_here"
  }
}
```

### Protected Admin Endpoints

#### GET /admin/profile
**Deskripsi**: Mendapatkan profil admin yang sedang login
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "admin": {
      "id": "uuid",
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "Administrator",
      "role": "superadmin"
    }
  }
}
```

#### PUT /admin/profile
**Deskripsi**: Update profil admin
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "full_name": "New Admin Name"
}
```
**Response**: Updated admin profile

#### POST /admin/change-password
**Deskripsi**: Ganti password admin
**Request Body**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "NewPassword123!"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

#### POST /admin/logout
**Deskripsi**: Logout admin
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

#### POST /admin/register (Superadmin only)
**Deskripsi**: Registrasi admin baru (hanya superadmin)
**Request Body**:
```json
{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "NewPassword123!",
  "full_name": "New Admin",
  "role": "admin"
}
```
**Response**: New admin data with token

---

## Authentication Headers

### User Authentication
```
Authorization: Bearer <jwt_token>
```

### Admin Authentication
```
Authorization: Bearer <admin_jwt_token>
```

### Internal Service Authentication
```
X-Service-Key: <internal_service_key>
X-Internal-Service: true
```

---

## Validation Rules

### User Registration
- **email**: Valid email format, max 255 characters
- **password**: Min 8 characters, must contain letters and numbers

### User Profile Update
- **full_name**: Max 100 characters (optional)
- **school_origin**: Max 150 characters (optional)
- **date_of_birth**: Valid date, cannot be in future (optional)
- **gender**: One of: male, female, other, prefer_not_to_say (optional)

### Admin Registration
- **username**: Alphanumeric, 3-100 characters
- **email**: Valid email format, max 255 characters
- **password**: Min 8 characters, must contain uppercase, lowercase, number, and special character
- **full_name**: Max 255 characters (optional)
- **role**: One of: admin, superadmin, moderator (default: admin)

---

## Notes

1. **Rate Limiting**: Auth endpoints memiliki rate limiting untuk keamanan
2. **Batch Processing**: User registration mendukung batch processing untuk performa yang lebih baik
3. **Password Security**: Admin passwords memiliki requirement yang lebih ketat
4. **Token Management**: JWT tokens digunakan untuk authentication dengan expiration time
5. **Role-based Access**: Admin endpoints memiliki role-based access control
