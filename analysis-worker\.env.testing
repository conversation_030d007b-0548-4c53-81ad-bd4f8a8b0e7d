# Environment for Testing with Mock AI
NODE_ENV=development

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
DEAD_LETTER_QUEUE=assessment_analysis_dlq

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Google Generative AI Configuration (not used in mock mode)
GOOGLE_AI_API_KEY=test_api_key_placeholder
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=4096

# Mock AI Configuration (for testing without using paid Gemini API)
# Set to 'true' to use mock AI responses instead of real Gemini API
USE_MOCK_MODEL=true

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002
ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production

# Notification Service Configuration
NOTIFICATION_SERVICE_URL=http://localhost:3004
NOTIFICATION_SERVICE_KEY=internal_service_secret_key_change_in_production

# Worker Configuration
WORKER_CONCURRENCY=10
HEARTBEAT_INTERVAL=30000
HEALTH_CHECK_INTERVAL=60000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/analysis-worker.log
