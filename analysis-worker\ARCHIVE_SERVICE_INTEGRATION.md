# Archive Service Integration - Analysis Worker

## Overview
Analysis Worker sudah terintegrasi dengan Archive Service dengan benar dan telah ditingkatkan untuk menggunakan fitur-fitur advanced yang tersedia.

## Fitur yang Digunakan

### ✅ Fitur yang Sudah Diimplementasi
1. **Basic Result Saving** - `POST /archive/results`
2. **Result Status Update** - `PUT /archive/results/:id`
3. **Proper Authentication** - Internal service headers
4. **Error Handling & Retry Logic**
5. **Health Check** - Monitoring Archive Service availability

### 🆕 Fitur Baru yang Ditambahkan
1. **Batch Processing Support** - Menggunakan `?batch=true` parameter
2. **Batch Results Endpoint** - `POST /archive/results/batch`
3. **Batch Queue Management** - `POST /archive/batch/process`
4. **Batch Statistics** - `GET /archive/batch/stats`
5. **Periodic Health Monitoring**

## API Endpoints yang Digunakan

### 1. Save Analysis Result (Enhanced)
```javascript
// Sekarang mendukung batch processing
await archiveService.saveAnalysisResult(userId, assessmentData, personaProfile, jobId, useBatch = true);
```

**Endpoint**: `POST /archive/results?batch=true`
**Headers**:
- `X-Internal-Service: true`
- `X-Service-Key: <service_key>`
- `Content-Type: application/json`

### 2. Batch Save Multiple Results (New)
```javascript
await archiveService.saveBatchAnalysisResults(results, jobId);
```

**Endpoint**: `POST /archive/results/batch`

### 3. Force Process Batch Queue (New)
```javascript
await archiveService.processBatchQueue(jobId);
```

**Endpoint**: `POST /archive/batch/process`

### 4. Get Batch Statistics (New)
```javascript
await archiveService.getBatchStats(jobId);
```

**Endpoint**: `GET /archive/batch/stats`

### 5. Health Check
```javascript
const isHealthy = await archiveService.checkHealth();
```

**Endpoint**: `GET /health`

## Configuration

### Environment Variables
```bash
# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002
ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production

# Health Check Configuration
HEALTH_CHECK_INTERVAL=60000  # 1 minute
```

## Error Handling

### Retryable Errors
- Network errors (ECONNREFUSED, ENOTFOUND, ETIMEDOUT)
- HTTP 5xx errors
- HTTP 429 (Too Many Requests)

### Non-Retryable Errors
- HTTP 4xx errors (except 429)
- Validation errors
- Authentication errors

## Monitoring & Logging

### Health Checks
- Startup health check before consuming messages
- Periodic health checks every 1 minute
- Health status logged for monitoring

### Batch Processing Monitoring
- Queue size monitoring
- Processing status tracking
- Success/failure statistics

## Performance Optimizations

### 1. Batch Processing
- Default menggunakan batch processing (`batch=true`)
- Mengurangi database load di Archive Service
- Meningkatkan throughput

### 2. Connection Pooling
- Axios instance dengan persistent connections
- Timeout configuration untuk reliability

### 3. Retry Logic
- Smart retry untuk transient errors
- Exponential backoff (dapat dikonfigurasi)

## Usage Examples

### Basic Usage (Current)
```javascript
// Otomatis menggunakan batch processing
const result = await archiveService.saveAnalysisResult(
  userId, 
  assessmentData, 
  personaProfile, 
  jobId
);
```

### Disable Batch Processing
```javascript
// Untuk kasus khusus yang memerlukan immediate processing
const result = await archiveService.saveAnalysisResult(
  userId, 
  assessmentData, 
  personaProfile, 
  jobId,
  false // useBatch = false
);
```

### Batch Multiple Results
```javascript
const results = [
  { user_id: 'user1', assessment_data: {}, persona_profile: {}, status: 'completed' },
  { user_id: 'user2', assessment_data: {}, persona_profile: {}, status: 'completed' }
];

const batchResult = await archiveService.saveBatchAnalysisResults(results, jobId);
```

## Troubleshooting

### Common Issues

1. **Archive Service Unavailable**
   - Worker akan tetap berjalan dan retry
   - Health check akan mendeteksi dan log status
   - Error akan di-retry sesuai konfigurasi

2. **Batch Queue Full**
   - Gunakan `processBatchQueue()` untuk force processing
   - Monitor dengan `getBatchStats()`

3. **Authentication Errors**
   - Pastikan `ARCHIVE_SERVICE_KEY` sama dengan Archive Service
   - Periksa header `X-Internal-Service` dan `X-Service-Key`

### Monitoring Commands
```bash
# Check batch queue status
curl -H "X-Internal-Service: true" \
     -H "X-Service-Key: your_key" \
     http://localhost:3002/archive/batch/stats

# Force process batch queue
curl -X POST \
     -H "X-Internal-Service: true" \
     -H "X-Service-Key: your_key" \
     http://localhost:3002/archive/batch/process
```

## Migration Notes

### Breaking Changes
- Tidak ada breaking changes
- Semua existing functionality tetap bekerja
- Batch processing diaktifkan secara default

### Backward Compatibility
- ✅ Existing code tetap berfungsi
- ✅ API signature kompatibel
- ✅ Environment variables sama

## Next Steps

1. **Monitor Performance**: Pantau improvement dari batch processing
2. **Tune Batch Size**: Sesuaikan batch size di Archive Service jika diperlukan
3. **Add Metrics**: Implementasi metrics untuk monitoring yang lebih detail
4. **Load Testing**: Test dengan high volume untuk validasi performance
