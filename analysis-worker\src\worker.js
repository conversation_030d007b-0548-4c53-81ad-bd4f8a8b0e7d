/**
 * Analysis Worker - Main Entry Point
 * 
 * This worker consumes assessment jobs from RabbitMQ,
 * processes them using Google Generative AI,
 * and saves results to Archive Service.
 */

// Load environment variables
const path = require('path');

// Load environment variables
require('dotenv').config();

// Import dependencies
const logger = require('./utils/logger');
const queueConsumer = require('./services/queueConsumer');
const archiveService = require('./services/archiveService');
const { gracefulShutdown } = require('./utils/shutdown');

// Log worker startup
logger.info('Analysis Worker starting up', {
  environment: process.env.NODE_ENV,
  queueName: process.env.QUEUE_NAME,
  workerConcurrency: process.env.WORKER_CONCURRENCY
});

/**
 * Main function to start the worker
 */
async function startWorker() {
  try {
    // Check Archive Service health before starting
    logger.info('Checking Archive Service health...');
    const archiveHealthy = await archiveService.checkHealth();

    if (!archiveHealthy) {
      logger.warn('Archive Service is not healthy, but continuing startup...');
      // Don't fail startup, just warn - service might come online later
    } else {
      logger.info('Archive Service is healthy');
    }

    // Initialize queue consumer
    await queueConsumer.initialize();

    // Start consuming messages
    await queueConsumer.startConsuming();

    // Log successful startup
    logger.info('Analysis Worker started successfully and is consuming messages');

    // Setup heartbeat for monitoring
    const heartbeatInterval = parseInt(process.env.HEARTBEAT_INTERVAL || '30000');
    setInterval(() => {
      logger.info('Worker heartbeat', {
        status: 'running',
        timestamp: new Date().toISOString()
      });
    }, heartbeatInterval);

    // Setup periodic health check for Archive Service
    const healthCheckInterval = parseInt(process.env.HEALTH_CHECK_INTERVAL || '60000'); // 1 minute
    setInterval(async () => {
      try {
        const healthy = await archiveService.checkHealth();
        logger.debug('Archive Service health check', { healthy });
      } catch (error) {
        logger.warn('Archive Service health check failed', { error: error.message });
      }
    }, healthCheckInterval);

  } catch (error) {
    logger.error('Failed to start Analysis Worker', {
      error: error.message,
      stack: error.stack
    });

    // Exit with error
    process.exit(1);
  }
}

// Handle process termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', {
    error: error.message,
    stack: error.stack
  });
  gracefulShutdown('uncaughtException');
});

// Start the worker
startWorker();
